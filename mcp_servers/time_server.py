#!/usr/bin/env python3
"""
简单的时间和系统信息MCP服务器
"""
import asyncio
import json
import sys
from datetime import datetime
import platform
import os
from typing import Any, Dict, List

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
except ImportError as e:
    print(f"MCP导入失败: {e}", file=sys.stderr)
    sys.exit(1)

# 创建服务器实例
server = Server("time-server")


@server.list_tools()
async def list_tools() -> list[Tool]:
    """列出可用工具"""
    return [
        Tool(
            name="get_current_time",
            description="获取当前时间",
            inputSchema={
                "type": "object",
                "properties": {
                    "format": {
                        "type": "string",
                        "description": "时间格式 (iso, timestamp, readable)",
                        "default": "readable"
                    }
                }
            }
        ),
        Tool(
            name="get_system_info",
            description="获取系统信息",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        ),
        Tool(
            name="calculate",
            description="执行简单的数学计算",
            inputSchema={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "数学表达式，如 '2 + 3 * 4'"
                    }
                },
                "required": ["expression"]
            }
        )
    ]


@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> list[TextContent]:
    """调用工具"""
    if name == "get_current_time":
        format_type = arguments.get("format", "readable")
        now = datetime.now()

        if format_type == "iso":
            time_str = now.isoformat()
        elif format_type == "timestamp":
            time_str = str(int(now.timestamp()))
        else:  # readable
            time_str = now.strftime("%Y年%m月%d日 %H:%M:%S")

        return [TextContent(type="text", text=f"当前时间: {time_str}")]

    elif name == "get_system_info":
        info = {
            "操作系统": platform.system(),
            "系统版本": platform.release(),
            "架构": platform.machine(),
            "处理器": platform.processor(),
            "Python版本": platform.python_version(),
            "主机名": platform.node(),
            "当前工作目录": os.getcwd()
        }

        info_text = "\n".join([f"{k}: {v}" for k, v in info.items()])
        return [TextContent(type="text", text=f"系统信息:\n{info_text}")]

    elif name == "calculate":
        expression = arguments.get("expression", "")
        try:
            # 安全的数学计算（只允许基本运算符）
            allowed_chars = set("0123456789+-*/.() ")
            if not all(c in allowed_chars for c in expression):
                raise ValueError("表达式包含不允许的字符")

            result = eval(expression)
            return [TextContent(type="text", text=f"计算结果: {expression} = {result}")]
        except Exception as e:
            return [TextContent(type="text", text=f"计算错误: {str(e)}")]

    else:
        raise ValueError(f"未知工具: {name}")


async def main():
    """主函数"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream, server.create_initialization_options())


if __name__ == "__main__":
    asyncio.run(main())
