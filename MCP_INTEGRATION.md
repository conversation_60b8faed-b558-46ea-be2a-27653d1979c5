# MCP服务集成说明

## 概述

本项目已集成MCP（Model Context Protocol）服务支持，允许用户配置和管理各种MCP服务。

## 功能特性

- ✅ JSON格式的MCP服务配置
- ✅ 每个服务的开关控制按钮
- ✅ 兼容efficiency现有形式，避免function call形式
- ✅ 动态变量支持，根据用户需求自动选择MCP服务
- ✅ 优雅的错误处理和兼容性支持

## 使用方法

### 1. 访问MCP配置页面

在应用的侧边栏中，点击"配置MCP服务"按钮即可进入MCP服务配置页面。

### 2. 配置MCP服务

#### 添加新服务

1. 在"添加新MCP服务"部分填写服务信息：
   - 服务名称：为服务指定一个唯一的名称
   - 选择预设服务或自定义配置
   - 设置命令和参数
   - 选择是否启用

2. 点击"添加"按钮保存配置

#### 管理现有服务

- 使用开关按钮启用或禁用服务
- 点击"删除"按钮移除不需要的服务
- 查看服务的详细配置信息

### 3. 导入/导出配置

- **导出配置**：点击"导出配置"按钮下载当前配置文件
- **导入配置**：使用文件上传器导入之前保存的配置文件

## 配置文件格式

MCP服务配置使用JSON格式，示例如下：

```json
{
  "mcpServers": {
    "weather-server": {
      "command": "python",
      "args": ["-m", "weather_mcp"],
      "enabled": true
    },
    "file-server": {
      "command": "python",
      "args": ["-m", "mcp_filesystem", "/app/data"],
      "enabled": false
    }
  }
}
```

## 在聊天中使用MCP服务

### 使用MCP工具

```xml
<use_mcp_tool>
<server_name>weather-server</server_name>
<tool_name>get_forecast</tool_name>
<arguments>
{
  "city": "Beijing",
  "days": 5
}
</arguments>
</use_mcp_tool>
```

### 访问MCP资源

```xml
<access_mcp_resource>
<server_name>file-server</server_name>
<uri>file:///app/data/example.txt</uri>
</access_mcp_resource>
```

## 自动服务选择

系统支持智能的服务选择功能：

1. **工具名称匹配**：如果指定的服务不存在，系统会自动查找提供该工具的服务
2. **URI匹配**：访问资源时，系统会根据URI前缀自动选择合适的服务
3. **回退机制**：如果没有找到匹配的服务，会使用第一个可用的服务

## 环境要求

### 基础功能

- Python 3.11+
- Streamlit
- 基础的efficiency项目依赖

### MCP功能

- Node.js 18+ （用于运行基于npm的MCP服务）
- MCP Python客户端库 (mcp==1.1.2)
- npx（随Node.js安装）

## 注意事项

1. **完整集成**：MCP功能已完全集成到efficiency项目中，参考LLaMa-MCP项目实现
2. **错误处理**：所有MCP相关操作都有完善的错误处理机制
3. **性能**：MCP服务的启动和停止是异步的，不会阻塞主应用
4. **安全性**：所有MCP服务都在受控环境中运行

## 故障排除

### Docker构建失败

如果Docker构建过程中出现Node.js相关错误：

1. 确保网络连接正常，能够访问Node.js官方源
2. 检查Docker构建日志，查看具体错误信息
3. 可能需要清理Docker缓存：`docker system prune -a`

### 服务启动失败

1. 检查服务配置是否正确
2. 确认命令和参数是否有效
3. 查看系统日志获取详细错误信息

### 配置文件问题

1. 确保JSON格式正确
2. 检查必需字段是否存在
3. 验证文件路径和权限

## 开发说明

### 文件结构

```
efficiency/
├── functions/
│   ├── mcp_client.py      # MCP客户端实现
│   └── mcp_server.py      # MCP服务端管理
├── pages/
│   └── mcp_config.py      # MCP配置页面
├── config/
│   └── mcp_config.json    # MCP配置文件
└── examples/
    └── tp.md              # 系统提示模板（包含MCP变量）
```

### 扩展开发

要添加新的MCP服务类型：

1. 在`mcp_config.py`中添加预设配置
2. 更新`mcp_client.py`中的服务选择逻辑
3. 在`tp.md`中添加相应的使用说明

## 更新日志

- v1.0.0: 初始版本，支持基础MCP服务配置和管理
- v1.1.0: 添加自动服务选择功能
- v1.2.0: 改进错误处理和兼容性支持
