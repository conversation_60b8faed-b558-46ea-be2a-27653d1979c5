#!/usr/bin/env python3
"""
简单测试MCP服务状态的脚本
"""
import subprocess
import json
import os

def test_mcp_services():
    """测试MCP服务状态"""
    print("=== MCP服务状态测试 ===")

    # 读取配置文件
    config_path = "config/mcp_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        servers = config.get("mcpServers", {})
        print(f"总服务数: {len(servers)}")

        for server_name, server_config in servers.items():
            enabled = server_config.get("enabled", False)
            command = server_config.get("command", "")
            args = server_config.get("args", [])

            print(f"\n🔍 服务: {server_name}")
            print(f"   状态: {'✅ 启用' if enabled else '⚪ 禁用'}")
            print(f"   命令: {command}")
            print(f"   参数: {' '.join(args)}")

            if enabled:
                # 检查命令是否可用
                try:
                    result = subprocess.run([command, "--version"],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print(f"   📋 命令可用: {result.stdout.strip()}")
                    else:
                        print(f"   ❌ 命令测试失败")
                except Exception as e:
                    print(f"   ❌ 命令不可用: {e}")
    else:
        print("❌ 配置文件不存在")

def test_sequential_thinking():
    """测试sequential thinking服务"""
    print("\n=== Sequential Thinking 服务测试 ===")

    # 检查npx是否可用
    try:
        result = subprocess.run(["npx", "--version"],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ npx 可用: {result.stdout.strip()}")

            # 尝试获取sequential thinking的帮助信息
            try:
                result = subprocess.run([
                    "npx", "-y", "@smithery/cli@latest", "run",
                    "@smithery-ai/server-sequential-thinking", "--help"
                ], capture_output=True, text=True, timeout=10)

                print(f"📋 Sequential Thinking 响应:")
                print(f"   返回码: {result.returncode}")
                if result.stdout:
                    print(f"   输出: {result.stdout[:200]}...")
                if result.stderr:
                    print(f"   错误: {result.stderr[:200]}...")

            except Exception as e:
                print(f"❌ 无法测试 sequential thinking: {e}")
        else:
            print("❌ npx 不可用")
    except Exception as e:
        print(f"❌ npx 测试失败: {e}")

if __name__ == "__main__":
    test_mcp_services()
    test_sequential_thinking()
