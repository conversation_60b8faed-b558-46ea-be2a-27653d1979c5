{"mcpServers": {"files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app/data"], "enabled": true}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["@wopal/mcp-server-hotnews"], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "duckduckgo-mcp-server": {"connection_type": "http", "server_name": "@nickclyde/duckduckgo-mcp-server", "api_key": "70032a0a-cd54-44d0-9f88-19d8afc747aa", "profile_id": "", "config": {}, "enabled": true}, "browserbase": {"connection_type": "http", "server_name": "@browserbasehq/mcp-browserbase", "api_key": "70032a0a-cd54-44d0-9f88-19d8afc747aa", "profile_id": "", "config": {"browserbaseApiKey": "", "browserbaseProjectId": ""}, "enabled": false}}}