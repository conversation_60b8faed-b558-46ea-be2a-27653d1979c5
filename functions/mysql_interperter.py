# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/17 3:45 下午
@Auth ： Jinx
@File ：mysql_interperter.py
@IDE ：PyCharm
"""
import subprocess

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional, Dict, List, Tuple, Any
import logging

from utils.format_conversion import list_to_markdown


class SQLConnection:
    """
    简化的SQL查询执行器，只支持查询语句

    支持以下数据库:
    - MySQL: mysql+pymysql://user:password@host:port/database
    - PostgreSQL: postgresql+psycopg2://user:password@host:port/database
    - SQLite: sqlite:///path/to/database.db
    - Oracle: oracle+cx_oracle://user:password@host:port/database
    - SQL Server: mssql+pyodbc://user:password@host:port/database?driver=ODBC+Driver+17+for+SQL+Server
    """

    def __init__(self, connection_string: str, echo: bool = False):
        """
        初始化数据库连接

        :param connection_string: SQLAlchemy格式的连接字符串
        :param echo: 是否输出SQL日志
        """
        self.connection_string = connection_string
        self.echo = echo
        self.engine = None
        self.connect()

    def connect(self):
        """建立数据库连接"""
        try:
            self.engine = create_engine(
                self.connection_string,
                echo=self.echo
            )
            logging.info("Database connection established successfully")
        except Exception as e:
            logging.error(f"Failed to establish database connection: {str(e)}")
            raise

    def execute_query(self, sql: str, params: Optional[Dict] = None) -> List[Dict]:
        """
        执行查询SQL语句，返回结果列表

        :param sql: SQL查询语句
        :param params: 参数字典
        :return: 结果列表，每行作为一个字典
        :raises: SQLAlchemyError 如果查询执行失败
        """
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(sql), params or {})
                return [dict(row._mapping) for row in result]
        except SQLAlchemyError as e:
            logging.error(f"Query execution failed: {str(e)}")
            raise

    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logging.info("Database connection closed")

    def __enter__(self):
        """支持with语句"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句"""
        self.close()

    def __del__(self):
        """析构时自动关闭连接"""
        self.close()


mysql_conn = SQLConnection(
    "mysql+pymysql://root:123456@localhost:3306/my_database",
    echo=True
)


def execute_sql(sql_str) -> Tuple[bool, str]:
    """
    在db中执行sql语句
    Args:
        sql_str:

    Returns:
    """
    success, result = False, ""
    try:
        result = mysql_conn.execute_query(sql_str)
        result = list_to_markdown(result)
    except subprocess.CalledProcessError as e:
        result = f"Command execution failed: {e}"
    return success, result


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # MySQL示例 - 注意密码最好不要直接写在代码中

    try:
        # 查询示例 - 建议SQL语句使用全大写或小写保持一致性
        results = mysql_conn.execute_query("SELECT * FROM economic_data")
        print(results)

    except Exception as e:
        print(f"查询出错: {str(e)}")

    finally:
        # 确保连接关闭
        mysql_conn.close()
