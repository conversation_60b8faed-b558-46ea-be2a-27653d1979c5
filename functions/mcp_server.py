"""
MCP服务端实现
"""
import os
import json
import logging
import asyncio
import subprocess
import time
from typing import Dict, Any, List, Optional, Tuple, Callable

# 导入MCP相关模块
from mcp import StdioServerParameters

MCP_AVAILABLE = True


class MCPServerManager:
    """MCP服务端管理器，负责管理MCP服务端进程"""

    def __init__(self, config_path: str = "config/mcp_config.json"):
        """初始化MCP服务端管理器

        Args:
            config_path: MCP配置文件路径
        """
        self.config_path = config_path
        self.server_processes = {}  # 存储运行中的MCP服务端进程
        self.server_logs = {}  # 存储服务日志
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载MCP配置

        Returns:
            MCP配置
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            else:
                # 创建默认配置
                default_config = {"mcpServers": {}}
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logging.error(f"加载MCP配置失败: {e}")
            return {"mcpServers": {}}

    def _save_config(self, config: Dict[str, Any]) -> None:
        """保存MCP配置

        Args:
            config: MCP配置
        """
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存MCP配置失败: {e}")

    def get_server_config(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取服务器配置

        Args:
            server_name: 服务器名称

        Returns:
            服务器配置
        """
        return self.config.get("mcpServers", {}).get(server_name)

    def get_all_servers(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务器配置

        Returns:
            所有服务器配置
        """
        return self.config.get("mcpServers", {})

    def update_server_config(self, server_name: str, config: Dict[str, Any]) -> None:
        """更新服务器配置

        Args:
            server_name: 服务器名称
            config: 服务器配置
        """
        if "mcpServers" not in self.config:
            self.config["mcpServers"] = {}
        self.config["mcpServers"][server_name] = config
        self._save_config(self.config)

    def remove_server_config(self, server_name: str) -> None:
        """删除服务器配置

        Args:
            server_name: 服务器名称
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            del self.config["mcpServers"][server_name]
            self._save_config(self.config)

            # 如果服务正在运行，停止它
            self.stop_server(server_name)

    def toggle_server_status(self, server_name: str, enabled: bool) -> None:
        """切换服务器状态

        Args:
            server_name: 服务器名称
            enabled: 是否启用
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            self.config["mcpServers"][server_name]["enabled"] = enabled
            self._save_config(self.config)

            # 根据状态启动或停止服务
            if enabled:
                self.start_server(server_name)
            else:
                self.stop_server(server_name)

    def start_server(self, server_name: str) -> bool:
        """启动MCP服务端

        Args:
            server_name: 服务器名称

        Returns:
            是否启动成功
        """
        server_config = self.get_server_config(server_name)
        if not server_config:
            logging.error(f"服务器配置不存在: {server_name}")
            return False

        if not server_config.get("enabled", False):
            logging.error(f"服务器未启用: {server_name}")
            return False

        # 如果服务已经在运行，先停止它
        if server_name in self.server_processes:
            self.stop_server(server_name)

        try:
            # 构建命令
            command = [server_config["command"]] + server_config["args"]

            # 设置环境变量
            env = os.environ.copy()
            if server_config.get("env"):
                env.update(server_config["env"])

            # 初始化日志
            self.server_logs[server_name] = {
                "stdout": [],
                "stderr": [],
                "command": " ".join(command),
                "start_time": time.time()
            }

            # 启动进程
            process = subprocess.Popen(
                command,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )

            # 存储进程
            self.server_processes[server_name] = process

            # 记录启动日志
            self.server_logs[server_name]["stdout"].append(f"[启动] 服务已启动，PID: {process.pid}")

            logging.info(f"已启动MCP服务端: {server_name}")
            return True
        except Exception as e:
            error_msg = f"启动MCP服务端失败: {server_name}, 错误: {e}"
            logging.error(error_msg)

            # 记录错误日志
            if server_name not in self.server_logs:
                self.server_logs[server_name] = {"stdout": [], "stderr": [], "command": "", "start_time": time.time()}
            self.server_logs[server_name]["stderr"].append(f"[错误] {error_msg}")

            return False

    def stop_server(self, server_name: str) -> bool:
        """停止MCP服务端

        Args:
            server_name: 服务器名称

        Returns:
            是否停止成功
        """
        if server_name in self.server_processes:
            try:
                process = self.server_processes[server_name]

                # 记录停止日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stdout"].append(f"[停止] 正在停止服务...")

                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    if server_name in self.server_logs:
                        self.server_logs[server_name]["stdout"].append(f"[停止] 强制终止服务")

                del self.server_processes[server_name]

                # 记录停止完成日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stdout"].append(f"[停止] 服务已停止")

                logging.info(f"已停止MCP服务端: {server_name}")
                return True
            except Exception as e:
                error_msg = f"停止MCP服务端失败: {server_name}, 错误: {e}"
                logging.error(error_msg)

                # 记录错误日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stderr"].append(f"[错误] {error_msg}")

                return False
        return True

    def start_all_enabled_servers(self) -> Dict[str, bool]:
        """启动所有启用的MCP服务端

        Returns:
            服务启动结果，键为服务名称，值为是否启动成功
        """
        results = {}
        for server_name, server_config in self.get_all_servers().items():
            if server_config.get("enabled", False):
                results[server_name] = self.start_server(server_name)
        return results

    def stop_all_servers(self) -> Dict[str, bool]:
        """停止所有MCP服务端

        Returns:
            服务停止结果，键为服务名称，值为是否停止成功
        """
        results = {}
        for server_name in list(self.server_processes.keys()):
            results[server_name] = self.stop_server(server_name)
        return results

    def get_server_status(self, server_name: str) -> str:
        """获取服务器状态

        Args:
            server_name: 服务器名称

        Returns:
            服务器状态，"running"表示运行中，"stopped"表示已停止，"disabled"表示已禁用，"not_found"表示未找到
        """
        server_config = self.get_server_config(server_name)
        if not server_config:
            return "not_found"

        if not server_config.get("enabled", False):
            return "disabled"

        if server_name in self.server_processes:
            process = self.server_processes[server_name]
            if process.poll() is None:
                return "running"
            else:
                # 进程已结束，清理
                del self.server_processes[server_name]
                return "stopped"

        return "stopped"

    def get_all_server_status(self) -> Dict[str, str]:
        """获取所有服务器状态

        Returns:
            所有服务器状态，键为服务名称，值为服务器状态
        """
        return {server_name: self.get_server_status(server_name) for server_name in self.get_all_servers()}

    def get_server_logs(self, server_name: str) -> Dict[str, Any]:
        """获取指定服务的日志

        Args:
            server_name: 服务名称

        Returns:
            服务日志信息
        """
        if server_name in self.server_logs:
            logs = self.server_logs[server_name].copy()

            # 如果服务正在运行，检查进程状态
            if server_name in self.server_processes:
                process = self.server_processes[server_name]

                # 检查进程状态
                if process.poll() is None:
                    logs["status"] = "运行中"
                else:
                    # 进程已结束
                    return_code = process.returncode
                    logs["status"] = f"已结束 (返回码: {return_code})"
                    if return_code != 0:
                        logs["stderr"].append(f"[错误] 进程异常退出，返回码: {return_code}")
            else:
                logs["status"] = "未运行"

            return logs
        else:
            return {"stdout": [], "stderr": [], "command": "", "start_time": 0, "status": "无日志"}

    def get_all_server_logs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务的日志

        Returns:
            所有服务的日志信息
        """
        all_logs = {}
        for server_name in self.server_logs:
            all_logs[server_name] = self.get_server_logs(server_name)
        return all_logs

    def clear_server_logs(self, server_name: str) -> bool:
        """清除指定服务的日志

        Args:
            server_name: 服务名称

        Returns:
            是否清除成功
        """
        if server_name in self.server_logs:
            self.server_logs[server_name]["stdout"] = []
            self.server_logs[server_name]["stderr"] = []
            return True
        return False


# 创建MCP服务端管理器实例
mcp_server_manager = MCPServerManager()
