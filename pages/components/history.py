"""
    __init__.py
"""
from typing import Tuple
import pyperclip
import streamlit as st


def exact_assistant_function(assistant_msg: str) -> Tuple[str, str]:
    """提取 assistant 的函数调用"""
    think = assistant_msg.split("\n")[0]
    function = assistant_msg.split("\n")[1]
    return think, function


# 显示历史消息，按问答对分组
def show_conversation_history():
    """"""
    i = 0
    while i < len(st.session_state.session.conversation) - 1:
        group_id = st.session_state.session.conversation[i].get("group_id")
        user_msg = st.session_state.session.conversation[i]
        assistant_msg = st.session_state.session.conversation[i + 1]

        if i not in st.session_state.quotes:
            st.session_state.quotes[i] = False

        # 显示问答对
        with st.container(border=True):
            col1, col2 = st.columns([0.9, 0.1])
            with col1:
                # 无论是否有 group_id，都先显示用户基础问题
                with st.chat_message(user_msg["role"]):
                    st.markdown(user_msg["content"])
                
                if group_id is None:
                    with st.chat_message(assistant_msg["role"]):
                        with st.expander(assistant_msg["content"][:80]):
                            st.markdown(assistant_msg["content"])
                    i += 2

                else:
                    while i + 2 < len(st.session_state.session.conversation) and \
                            st.session_state.session.conversation[i + 2].get("group_id") == group_id:
                        
                        assistant_msg = st.session_state.session.conversation[i + 1]
                        think, function = exact_assistant_function(assistant_msg["content"])
                        tool_msg = st.session_state.session.conversation[i + 2]
                        with st.chat_message(assistant_msg["role"]):
                            with st.expander(function[:20]):
                                st.markdown(f"```markdown\n{assistant_msg['content']}\n```")
                                st.markdown(f"Tool's response: \n```markdown\n{tool_msg['content']}\n```")
                        i += 2

                    assistant_msg = st.session_state.session.conversation[i + 1]
                    with st.chat_message(assistant_msg["role"]):
                        st.markdown(assistant_msg["content"])
                    i += 2
            with col2:
                if st.checkbox("Quote", key=f"quote_{i}"):
                    # 检查是否已经引用过
                    if user_msg["id"] not in st.session_state.quoted_ids:
                        # 拼接被引用的对话历史
                        st.session_state.quoted_history.extend([user_msg, assistant_msg])
                        st.session_state.quoted_ids.add(user_msg["id"])
                # Copy buttons
                if st.button("Copy User Message", key=f"copy_user_{i}"):
                    pyperclip.copy(user_msg["content"])  # Copy to clipboard
                    st.success("User message copied to clipboard!")

                if st.button("Copy Assistant Message", key=f"copy_assistant_{i}"):
                    pyperclip.copy(assistant_msg["content"])  # Copy to clipboard
                    st.success("Assistant message copied to clipboard!")


def clear_quotes():
    """"""
    # TODO ask streamlit to fix this
    # for key in st.session_state.keys():
    #     if key.startswith("quote_"):
    #         st.session_state[key] = False
    # 只修改中间变量，不触碰 Widget 的 key
    for i in st.session_state.quotes:
        st.session_state.quotes[i] = False

    st.session_state.quoted_history = []
    st.session_state.quoted_ids = set()
    st.rerun()
